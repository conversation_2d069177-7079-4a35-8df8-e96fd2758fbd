../../Scripts/__pycache__/fixup_firestore_admin_v1_keywords.cpython-312.pyc,,
../../Scripts/__pycache__/fixup_firestore_v1_keywords.cpython-312.pyc,,
../../Scripts/fixup_firestore_admin_v1_keywords.py,sha256=gBmWtp56s9KR-xAiw5IfELtgP-cqZhI99oR0flW1vlY,7802
../../Scripts/fixup_firestore_v1_keywords.py,sha256=P949waHhA2WlJW4yju5XMcC7Z_-Bw159-npHOOLkp60,7505
google/cloud/firestore/__init__.py,sha256=pe7mrRxTDQzRYEG4q6Gb1VADj8ThqD2UDZLQiDUQx2g,3413
google/cloud/firestore/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/firestore/gapic_version.py,sha256=uU9-nHQTLgvkTq39ssye313A8wOtPW59-Uv_M664rwY,653
google/cloud/firestore_admin_v1/__init__.py,sha256=uL69T_lG86f7NYMy0amW20r8_t0iI9zR-KQfRbAUp3Y,2282
google/cloud/firestore_admin_v1/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_admin_v1/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/firestore_admin_v1/gapic_metadata.json,sha256=FIjQ72TRE6MHTuJuYdkzkLgpgkUtTO8ZtpX9F7AvTrU,12219
google/cloud/firestore_admin_v1/gapic_version.py,sha256=uU9-nHQTLgvkTq39ssye313A8wOtPW59-Uv_M664rwY,653
google/cloud/firestore_admin_v1/py.typed,sha256=986zLtpMXwL-7nXabn6tbnx8AMKbxkX0e3KeCdrySvA,89
google/cloud/firestore_admin_v1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/firestore_admin_v1/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__init__.py,sha256=MZ0Zyh3IRUsgpT30U8AJxrSynbtD7zyXzFsdUElLV1M,769
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/async_client.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/client.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/pagers.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/async_client.py,sha256=ku1K5T_T7lspqKu-SeiDvxb0dCz6P-KVdqdOXnjb_Zs,185988
google/cloud/firestore_admin_v1/services/firestore_admin/client.py,sha256=ujrDFVGc1P74QmQ0X__QG9rLt9taZPzGPjDiQMB_j0c,204169
google/cloud/firestore_admin_v1/services/firestore_admin/pagers.py,sha256=xI4bLWxN5b2m11yi8a4h5dx63DlGXKLsfRdcy-Z5qsg,14215
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__init__.py,sha256=mI8EH1ZSJhb1OBk3AuEH5OG3_nDuM2gVeHx5vvtDjms,1418
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/base.py,sha256=tfLnoTV7wJRzsEYhFhZeCXnwJdb6hsZncQQERBpVY58,24589
google/cloud/firestore_admin_v1/services/firestore_admin/transports/grpc.py,sha256=VBJpB0wSJq_QqjMsSBDDPi4k9AusS3pvsewlvhl-ZZw,59766
google/cloud/firestore_admin_v1/services/firestore_admin/transports/grpc_asyncio.py,sha256=_8s3Nvurz9-I42plKNraUhtudeG5T0_qTYnr4Zw2tic,70508
google/cloud/firestore_admin_v1/services/firestore_admin/transports/rest.py,sha256=Kg2ZZ8AkNAfydaKXLv2JNMffwizoT-HkygxzjbYFv9k,301026
google/cloud/firestore_admin_v1/services/firestore_admin/transports/rest_base.py,sha256=Qv44d8rd17Aki9ShYIzShMeWHp1XCr2P0VPdIOgfOUA,63435
google/cloud/firestore_admin_v1/types/__init__.py,sha256=trFX9SBsM3JlDanwTDS8BbFAM807jGGLue78PEXHUAA,3967
google/cloud/firestore_admin_v1/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/backup.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/database.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/field.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/firestore_admin.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/index.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/location.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/operation.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/schedule.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/user_creds.cpython-312.pyc,,
google/cloud/firestore_admin_v1/types/backup.py,sha256=5r2nBWkgkwT24Je6wY3MSOFoakzX2qCk3KyQnOzovNE,4634
google/cloud/firestore_admin_v1/types/database.py,sha256=MXUS82WwxSvBkJDRbM0yq8LSlLhtQ9COvDX4s2als-A,20712
google/cloud/firestore_admin_v1/types/field.py,sha256=S32zfHVZH3xFb0awFySQXkFYhfeGjRjxiDEmPWFAm3U,7576
google/cloud/firestore_admin_v1/types/firestore_admin.py,sha256=-DzyUClkdifaicbvvh-AMjnw0UDlBuAeJS6eJIVfeEE,33627
google/cloud/firestore_admin_v1/types/index.py,sha256=DoNTgFtn3nlr5MewVpwiN4kvABaXBVqFHTi5vi_gmv0,14065
google/cloud/firestore_admin_v1/types/location.py,sha256=Lmy57-4fBDwPuxGfS2Thie7rloBZafPeXiG4FQJMpCE,1061
google/cloud/firestore_admin_v1/types/operation.py,sha256=msO7NPh7sKDYTsHnuzGDlkp8NjaR6JLPnTALZv4ZerA,19337
google/cloud/firestore_admin_v1/types/schedule.py,sha256=x7xEx6zaaRl-nknxUK-HlOF7W6xOTg1dtoun_t0ElgQ,4677
google/cloud/firestore_admin_v1/types/user_creds.py,sha256=VdgFxd24IbUz-o7_hEVlAOKCkGBAm-04qXe9xlResTM,3777
google/cloud/firestore_bundle/__init__.py,sha256=yAKXllHqufAourV3bQV0cznY7gOBiK2DWQwhxdcSSGk,1114
google/cloud/firestore_bundle/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_bundle/__pycache__/_helpers.cpython-312.pyc,,
google/cloud/firestore_bundle/__pycache__/bundle.cpython-312.pyc,,
google/cloud/firestore_bundle/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/firestore_bundle/_helpers.py,sha256=rs3YY_LgD6RRKR3ay2p9gM_1uwBk6hxEY032f_hSNXg,361
google/cloud/firestore_bundle/bundle.py,sha256=jkEFHhwzyuTghMBdda8X_uNZaK_1kiS8ddR0U5feLSk,13930
google/cloud/firestore_bundle/gapic_metadata.json,sha256=WP2fXupExiCP8Mz5jm4QoaOURqGtnA6tv5CGx_cq6A8,231
google/cloud/firestore_bundle/gapic_version.py,sha256=uU9-nHQTLgvkTq39ssye313A8wOtPW59-Uv_M664rwY,653
google/cloud/firestore_bundle/py.typed,sha256=22rn8vq8SChWrPBibDFfX0tiViQW0mXXsTsih71_Ye8,80
google/cloud/firestore_bundle/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/firestore_bundle/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_bundle/types/__init__.py,sha256=2vYLzBPmp5VjR9X3t_6q0irxZVb8Hs_CoOFKYBrTcpg,853
google/cloud/firestore_bundle/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_bundle/types/__pycache__/bundle.cpython-312.pyc,,
google/cloud/firestore_bundle/types/bundle.py,sha256=NF8-uDb7vEq0C5In-VJHRmxVK0tT4NCI5JH60AsNCxg,7500
google/cloud/firestore_v1/__init__.py,sha256=2ri8gQX-IN62XQsHO8SdLtgaEreHcNTxEtdJ5XMjqmQ,5358
google/cloud/firestore_v1/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/_helpers.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/aggregation.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_aggregation.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_batch.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_client.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_collection.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_document.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_query.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_stream_generator.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_transaction.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/async_vector_query.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_aggregation.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_batch.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_client.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_collection.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_document.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_query.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_transaction.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/base_vector_query.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/batch.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/bulk_batch.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/bulk_writer.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/client.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/collection.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/document.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/field_path.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/order.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/query.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/query_profile.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/query_results.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/rate_limiter.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/stream_generator.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/transaction.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/transforms.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/vector.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/vector_query.cpython-312.pyc,,
google/cloud/firestore_v1/__pycache__/watch.cpython-312.pyc,,
google/cloud/firestore_v1/_helpers.py,sha256=a4stGLbESwulVI22TdlnP6DyWUTiGb3GBO4UNZXj0_E,46583
google/cloud/firestore_v1/aggregation.py,sha256=Ixm120R_h92Jtf8R8utCWBGXH5vQ7zPhctoYuLBvAK4,9663
google/cloud/firestore_v1/async_aggregation.py,sha256=Gcwzrhi4yeiHOddRRgwuXW8yfZAArv2u7SMtgfKxP90,8667
google/cloud/firestore_v1/async_batch.py,sha256=Tt6N_ZNTPotScINI2BDk-LIzpYAxPj61MdzTTB6HVK0,2873
google/cloud/firestore_v1/async_client.py,sha256=WDDZJz16UuVUts0SXHc_U47UNP0N0_bJg-Tr4zO8Lk8,15621
google/cloud/firestore_v1/async_collection.py,sha256=yE9cvETP-P1T3mQ3CashJqxk5i238iSHsnvYQ7rC_S8,11693
google/cloud/firestore_v1/async_document.py,sha256=S2N7No6wSidzcch3FBPounVIwBu0B_jVHg96UD6efJE,16391
google/cloud/firestore_v1/async_query.py,sha256=y2tYBPDZUzIpL0QYulj-fYazqC40IkEW7MVA6HGrzqo,22574
google/cloud/firestore_v1/async_stream_generator.py,sha256=MoOX3syGOxxTmw7l0neAxRliRpOAB2vYE7fSE9pnPpY,4396
google/cloud/firestore_v1/async_transaction.py,sha256=UKj_0LYFlF5EeDKOui7_O1DLPGKdLYmHoGr45pbNV0I,13438
google/cloud/firestore_v1/async_vector_query.py,sha256=Ah1zrlQ8hGYSg-s-nAzouLCgkn7cVoBFUezodUqU0Cg,8980
google/cloud/firestore_v1/base_aggregation.py,sha256=wJZRWgjqlItgJUTN6MINRb42N6aXXISxvOKGELOrezU,11288
google/cloud/firestore_v1/base_batch.py,sha256=071kCxH73zBmahvk-pBgAkuE42odRHFUU-gzYMOBfIQ,7486
google/cloud/firestore_v1/base_client.py,sha256=f4sQNwZN0w7i2ryX2yi1RS1EVIASV-NUBd8T9HCWT5w,22976
google/cloud/firestore_v1/base_collection.py,sha256=o3tOGVcQ3A0CTmZklDuXO9Sq7HqVs1tgl4n8nBvDL6w,22710
google/cloud/firestore_v1/base_document.py,sha256=8ob97iRDq8nHbf0VaaiYY1qRKdQGiGVQiegLiV_oKxI,19125
google/cloud/firestore_v1/base_query.py,sha256=Atzmtf002yr4QekH52fUf-nAYoOtrHlbBsh5rdC8ets,57962
google/cloud/firestore_v1/base_transaction.py,sha256=xdJvEZOQe3FebulXSYXzQvAuura5YBaPvMimBPQsLmU,6981
google/cloud/firestore_v1/base_vector_query.py,sha256=GcaVpSM9rNJo6JXU5yL5WExEuVsjquRnv6ls729EnGU,6296
google/cloud/firestore_v1/batch.py,sha256=PczgsUAs4dLpzYq0KZHHdC3NuyoVw6xGc5DObSQw6kw,2910
google/cloud/firestore_v1/bulk_batch.py,sha256=eP5-5EoxPgCptlLiw5uSZJvbLrxWt7PYdNgHdeOMJBY,3962
google/cloud/firestore_v1/bulk_writer.py,sha256=6MIscIWZNv1uoW8gTuBGJgv4m3s-Obf34izMz4t0K-s,35319
google/cloud/firestore_v1/client.py,sha256=S2h3qwdLek0UIkZCo90szSw7c2je2eFslr3LZPCEXZ0,14414
google/cloud/firestore_v1/collection.py,sha256=uZsfUBrtiln5-WSnP9JeQIFrV9CdVCnjm1OXR0OJyVo,11882
google/cloud/firestore_v1/document.py,sha256=Ia7d_9MYFTHdMIwYUUCbgFAZF-AoO1DA0L2HzdJVWqs,19211
google/cloud/firestore_v1/field_path.py,sha256=PBhIOYpn_n9iyUa0ZYQ1Db6BTwdE6TS-civd_wMmbJU,12333
google/cloud/firestore_v1/gapic_metadata.json,sha256=9gKxXCVZOEXa_n32e0FyRurohH5hmCj8bEEea1O754o,6391
google/cloud/firestore_v1/gapic_version.py,sha256=uU9-nHQTLgvkTq39ssye313A8wOtPW59-Uv_M664rwY,653
google/cloud/firestore_v1/order.py,sha256=1bCvOORgsTyBo672M2Y8xpEKKprXbLuhaSRMLUfhCmU,8450
google/cloud/firestore_v1/py.typed,sha256=9t7_u2uES6wHzMi1u2-nkdhxS1pAhtHKqubDWkRnMsQ,83
google/cloud/firestore_v1/query.py,sha256=2cpbxxVTroTozxo__X4IITJmYu7UAVS5Ki2ObUibXMk,24121
google/cloud/firestore_v1/query_profile.py,sha256=NeiBNnPJBROAMBv4GVbjrg40Yy6zztY7lP22urhopEA,4704
google/cloud/firestore_v1/query_results.py,sha256=DQXBgcJCgYcZcKpJIt1w1g1TshetOURljI5QoJZ3H4I,3363
google/cloud/firestore_v1/rate_limiter.py,sha256=FyJWZwDlueaMx8W2q67Webp63TwyJy1kWAHK8Ak4GC4,7688
google/cloud/firestore_v1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/firestore_v1/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/__init__.py,sha256=plTJWZDdz4UCKPoiW_6ZWbo13gi6b-ac2ofv-jBp_CM,749
google/cloud/firestore_v1/services/firestore/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/async_client.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/client.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/pagers.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/async_client.py,sha256=k7YGklQYETLMwjkxJYH73312KSfsQQHvHmgDwnlqriA,92184
google/cloud/firestore_v1/services/firestore/client.py,sha256=v3PFj_MEKhH1y3hYVkLkI48MSqCJq4YsANZVFqfK87Y,106899
google/cloud/firestore_v1/services/firestore/pagers.py,sha256=Xdp0w-LrOz58ltaXpPG87DzGdop3WC2Gjo0qEL-aUoE,20630
google/cloud/firestore_v1/services/firestore/transports/__init__.py,sha256=ytGRx17agB8n7fXKwNIGmBZa2APQwp-pYAf3M6Hqxn0,1348
google/cloud/firestore_v1/services/firestore/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/firestore_v1/services/firestore/transports/base.py,sha256=3xdD9bcRW3wv0W5uBU4fdqMWTmkIB_wYhasv9_pvs6o,22404
google/cloud/firestore_v1/services/firestore/transports/grpc.py,sha256=gaxbGvetR8L0-WnlPxRjHjXW7Yv-K7ZvBdCVQk7JlZY,37496
google/cloud/firestore_v1/services/firestore/transports/grpc_asyncio.py,sha256=qjcOLjark81lM2n5-nD1sysXlErOH5fzy0rVvUB200o,49971
google/cloud/firestore_v1/services/firestore/transports/rest.py,sha256=l-Nu9VGYrKiolT7ymQ43k9RlfS1L0Z_-qa3_w-4plYA,149191
google/cloud/firestore_v1/services/firestore/transports/rest_base.py,sha256=v62ZDp4yY-7c9_669i-oPhvPTuiji4VL57eBaxxtNII,35361
google/cloud/firestore_v1/stream_generator.py,sha256=ozcKa-Z773zokjqVCvhxXhehLlKgo14Zj8aKqtYZzC0,4151
google/cloud/firestore_v1/transaction.py,sha256=FWnCj8jVaW7bLoLwOItGyqzx9urb5f2NRrax3op2aJI,13051
google/cloud/firestore_v1/transforms.py,sha256=8G3r-fmWvOmIexQmUJ6Zd1zQFR504tmsbQOGGzCQBCw,4882
google/cloud/firestore_v1/types/__init__.py,sha256=-089Xt4A0dyfONey-sa9ZZZeZ5kxNpJu6DRcjEqojr0,3263
google/cloud/firestore_v1/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/aggregation_result.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/bloom_filter.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/common.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/document.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/firestore.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/query.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/query_profile.cpython-312.pyc,,
google/cloud/firestore_v1/types/__pycache__/write.cpython-312.pyc,,
google/cloud/firestore_v1/types/aggregation_result.py,sha256=oH8cl1uDkRU73J3eq6C1FKsZLOvXJNh5D-tknPxwVgM,1901
google/cloud/firestore_v1/types/bloom_filter.py,sha256=_h4Dtiyw5Q4fqUSJfX14t2du5u6WiVD2n-YG0dbB5Vs,3480
google/cloud/firestore_v1/types/common.py,sha256=EXmS-mHn3YOGA8S4vriMmi9f9dYVUK4LbfQU5krmOao,5530
google/cloud/firestore_v1/types/document.py,sha256=LqIAW75y883z9Gx4MSMjojMLot3QnqfJWG3-IiyXk_Y,9316
google/cloud/firestore_v1/types/firestore.py,sha256=6hF_-n-_f7fLQ32RJgT1Hs8h3s_QPSUDCXdrqHneUTA,61536
google/cloud/firestore_v1/types/query.py,sha256=q9oERRDMH0IUxskS6LL-GtFMtlnF7_74KHfc9WM88PQ,33268
google/cloud/firestore_v1/types/query_profile.py,sha256=XCgQU_5RU93oFe5A9A09D1N6pQDFyThrSFMcd2wnY70,4377
google/cloud/firestore_v1/types/write.py,sha256=pfKx-cMWXM_y3InDTdFqLFQZqJOKLyCQg4MWGNZKv7E,19730
google/cloud/firestore_v1/vector.py,sha256=Dv6U5QQHtT5d8Lu6llfF9YhR_6cyfY4BcM7dsKhh98o,1420
google/cloud/firestore_v1/vector_query.py,sha256=5D4PWdIseXACGHSD5xPAAUXgpKaDse-PWjHWd75K4nc,9132
google/cloud/firestore_v1/watch.py,sha256=dtkAqcpwJfxx6ZEsqzYp2gfLeo1wwy2A1CgPaEUA-2M,25710
google_cloud_firestore-2.21.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_firestore-2.21.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_firestore-2.21.0.dist-info/METADATA,sha256=EH-AeQ05D_ItYTalmikLTHylkZnaoFyN2op9QwrZoiw,9856
google_cloud_firestore-2.21.0.dist-info/RECORD,,
google_cloud_firestore-2.21.0.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
google_cloud_firestore-2.21.0.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
