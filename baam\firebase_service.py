import os
import firebase_admin
from firebase_admin import credentials, firestore
from typing import Dict, List, Optional, Any
import uuid
from datetime import datetime

class FirebaseService:
    _instance = None
    _db = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FirebaseService, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Initialize Firebase Admin SDK"""
        if not firebase_admin._apps:
            # Load service account key from environment variable
            service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT_KEY_PATH')
            if service_account_path and os.path.exists(service_account_path):
                cred = credentials.Certificate(service_account_path)
                firebase_admin.initialize_app(cred)
            else:
                # For development, you can use default credentials
                # Make sure to set GOOGLE_APPLICATION_CREDENTIALS environment variable
                try:
                    firebase_admin.initialize_app()
                except Exception as e:
                    print(f"Warning: Firebase initialization failed: {e}")
                    print("Please set up Firebase credentials. See FIREBASE_SETUP.md for instructions.")
                    # Initialize with a dummy app for development
                    firebase_admin.initialize_app(options={'projectId': 'dummy-project'})

        try:
            self._db = firestore.client()
        except Exception as e:
            print(f"Warning: Firestore client initialization failed: {e}")
            self._db = None

    @property
    def db(self):
        if self._db is None:
            raise Exception("Firebase is not properly configured. Please set up your service account credentials.")
        return self._db

    # User operations
    def create_user(self, user_data: Dict[str, Any]) -> str:
        """Create a new user"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        user_ref = self._db.collection('users').document(user_data['uid'])
        user_ref.set(user_data)
        return user_data['uid']

    def get_user(self, uid: str) -> Optional[Dict[str, Any]]:
        """Get user by UID"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        user_ref = self._db.collection('users').document(uid)
        doc = user_ref.get()
        return doc.to_dict() if doc.exists else None

    def update_user(self, uid: str, user_data: Dict[str, Any]) -> bool:
        """Update user data"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        user_ref = self._db.collection('users').document(uid)
        user_data['updated_at'] = datetime.utcnow().isoformat()
        user_ref.update(user_data)
        return True

    # Goal operations
    def create_goal(self, goal_data: Dict[str, Any]) -> str:
        """Create a new goal"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        goal_id = str(uuid.uuid4())
        goal_data['goal_id'] = goal_id
        goal_ref = self._db.collection('goals').document(goal_id)
        goal_ref.set(goal_data)
        return goal_id

    def get_goals_by_user(self, uid: str) -> List[Dict[str, Any]]:
        """Get all goals for a user"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        goals_ref = self._db.collection('goals').where('uid', '==', uid)
        docs = goals_ref.stream()
        return [doc.to_dict() for doc in docs]

    def get_goal(self, goal_id: str) -> Optional[Dict[str, Any]]:
        """Get goal by ID"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        goal_ref = self._db.collection('goals').document(goal_id)
        doc = goal_ref.get()
        return doc.to_dict() if doc.exists else None

    def update_goal(self, goal_id: str, goal_data: Dict[str, Any]) -> bool:
        """Update goal data"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        goal_ref = self._db.collection('goals').document(goal_id)
        goal_data['updated_at'] = datetime.utcnow().isoformat()
        goal_ref.update(goal_data)
        return True

    def delete_goal(self, goal_id: str) -> bool:
        """Delete a goal"""
        if self._db is None:
            raise Exception("Firebase is not configured. Please set up your service account credentials.")
        goal_ref = self._db.collection('goals').document(goal_id)
        goal_ref.delete()
        return True

    # Task operations
    def create_task(self, task_data: Dict[str, Any]) -> str:
        """Create a new task"""
        task_id = str(uuid.uuid4())
        task_data['task_id'] = task_id
        task_ref = self._db.collection('tasks').document(task_id)
        task_ref.set(task_data)
        return task_id

    def get_tasks_by_user(self, uid: str) -> List[Dict[str, Any]]:
        """Get all tasks for a user"""
        tasks_ref = self._db.collection('tasks').where('uid', '==', uid)
        docs = tasks_ref.stream()
        return [doc.to_dict() for doc in docs]

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        task_ref = self._db.collection('tasks').document(task_id)
        doc = task_ref.get()
        return doc.to_dict() if doc.exists else None

    def update_task(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """Update task data"""
        task_ref = self._db.collection('tasks').document(task_id)
        task_data['updated_at'] = datetime.utcnow().isoformat()
        task_ref.update(task_data)
        return True

    def delete_task(self, task_id: str) -> bool:
        """Delete a task"""
        task_ref = self._db.collection('tasks').document(task_id)
        task_ref.delete()
        return True

    def get_tasks_by_goal(self, goal_id: str) -> List[Dict[str, Any]]:
        """Get all tasks for a specific goal"""
        tasks_ref = self._db.collection('tasks').where('goal_id', '==', goal_id)
        docs = tasks_ref.stream()
        return [doc.to_dict() for doc in docs]

    # Conversation operations
    def create_conversation(self, conversation_data: Dict[str, Any]) -> str:
        """Create a new conversation"""
        conversation_id = str(uuid.uuid4())
        conversation_data['conversation_id'] = conversation_id
        conv_ref = self._db.collection('conversations').document(conversation_id)
        conv_ref.set(conversation_data)
        return conversation_id

    def get_conversations_by_user(self, uid: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversations for a user (most recent first)"""
        convs_ref = (self._db.collection('conversations')
                    .where('uid', '==', uid)
                    .order_by('timestamp', direction=firestore.Query.DESCENDING)
                    .limit(limit))
        docs = convs_ref.stream()
        return [doc.to_dict() for doc in docs]

    def get_conversation(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation by ID"""
        conv_ref = self._db.collection('conversations').document(conversation_id)
        doc = conv_ref.get()
        return doc.to_dict() if doc.exists else None

    # AI Insights operations
    def create_ai_insight(self, insight_data: Dict[str, Any]) -> str:
        """Create a new AI insight"""
        insight_id = str(uuid.uuid4())
        insight_data['insight_id'] = insight_id
        insight_ref = self._db.collection('ai_insights').document(insight_id)
        insight_ref.set(insight_data)
        return insight_id

    def get_ai_insights_by_user(self, uid: str) -> List[Dict[str, Any]]:
        """Get AI insights for a user"""
        insights_ref = self._db.collection('ai_insights').where('uid', '==', uid)
        docs = insights_ref.stream()
        return [doc.to_dict() for doc in docs]

    # Schedule Optimization operations
    def create_schedule_optimization(self, optimization_data: Dict[str, Any]) -> str:
        """Create a new schedule optimization"""
        optimization_id = str(uuid.uuid4())
        optimization_data['optimization_id'] = optimization_id
        opt_ref = self._db.collection('schedule_optimizations').document(optimization_id)
        opt_ref.set(optimization_data)
        return optimization_id

    def get_schedule_optimizations_by_user(self, uid: str) -> List[Dict[str, Any]]:
        """Get schedule optimizations for a user"""
        opts_ref = self._db.collection('schedule_optimizations').where('uid', '==', uid)
        docs = opts_ref.stream()
        return [doc.to_dict() for doc in docs]
