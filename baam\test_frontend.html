<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BAAM API Test Frontend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 BAAM API Test Frontend</h1>
        <p>Test your scheduling API endpoints with authentication</p>
        
        <div>
            <h3>Health Check (No Auth Required)</h3>
            <button onclick="testHealthCheck()">Test Health Check</button>
        </div>
        
        <div>
            <h3>User Endpoints (Test Auth)</h3>
            <button onclick="getUser()">Get Demo User</button>
            <button onclick="getUserGoals()">Get User Goals</button>
            <button onclick="getUserTasks()">Get User Tasks</button>
        </div>
        
        <div>
            <h3>Create New Data</h3>
            <button onclick="createGoal()">Create Sample Goal</button>
            <button onclick="createTask()">Create Sample Task</button>
        </div>
        
        <div id="response-container">
            <h3>Response:</h3>
            <div id="response" class="response">Click a button to test an endpoint...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';
        const TEST_USER_ID = 'demo_user_123';
        
        function showResponse(data, isError = false) {
            const responseDiv = document.getElementById('response');
            responseDiv.textContent = JSON.stringify(data, null, 2);
            responseDiv.className = `response ${isError ? 'error' : 'success'}`;
        }
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Test-Auth-Uid': TEST_USER_ID,
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    showResponse({
                        error: `HTTP ${response.status}`,
                        message: data.error || 'Unknown error',
                        details: data
                    }, true);
                } else {
                    showResponse(data);
                }
            } catch (error) {
                showResponse({
                    error: 'Network Error',
                    message: error.message
                }, true);
            }
        }
        
        // Health check (no auth required)
        function testHealthCheck() {
            makeRequest(`${API_BASE}/`);
        }
        
        // User endpoints
        function getUser() {
            makeRequest(`${API_BASE}/api/users/${TEST_USER_ID}`);
        }
        
        function getUserGoals() {
            makeRequest(`${API_BASE}/api/users/${TEST_USER_ID}/goals`);
        }
        
        function getUserTasks() {
            makeRequest(`${API_BASE}/api/users/${TEST_USER_ID}/tasks`);
        }
        
        // Create new data
        function createGoal() {
            const goalData = {
                uid: TEST_USER_ID,
                title: "Web Test Goal",
                description: "A goal created from the web frontend",
                status: "active",
                target_date: "2024-12-31",
                categories: ["web-test"]
            };
            
            makeRequest(`${API_BASE}/api/goals`, {
                method: 'POST',
                body: JSON.stringify(goalData)
            });
        }
        
        function createTask() {
            const taskData = {
                uid: TEST_USER_ID,
                title: "Web Test Task",
                description: "A task created from the web frontend",
                status: "planned",
                estimated_duration: 30,
                categories: ["web-test"]
            };
            
            makeRequest(`${API_BASE}/api/tasks`, {
                method: 'POST',
                body: JSON.stringify(taskData)
            });
        }
    </script>
</body>
</html>
